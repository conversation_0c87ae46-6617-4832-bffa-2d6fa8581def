import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { TreeNode } from "../../../context/types";

interface ContentFormProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

/**
 * ContentForm handles node-specific content properties
 * Used in the "Nội dung" (Content) tab of the Properties Panel
 */
export const ContentForm: React.FC<ContentFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const renderContentByType = () => {
    switch (node.type) {
      case "text":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="text-content">Nội dung văn bản</Label>
              <Textarea
                id="text-content"
                value={(node.properties?.content as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      content: e.target.value,
                    },
                  })
                }
                placeholder="Nhập nội dung văn bản..."
                rows={4}
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "image":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="image-src" className="block mb-2">
                Đường dẫn hình ảnh
              </Label>
              <Textarea
                id="image-src"
                value={(node.properties?.src as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      src: e.target.value,
                    },
                  })
                }
                placeholder="https://example.com/image.jpg"
                rows={2}
                disabled={disabled}
              />
            </div>
            <div>
              <Label htmlFor="image-alt" className="block mb-2">
                Mô tả hình ảnh
              </Label>
              <Textarea
                id="image-alt"
                value={(node.properties?.alt as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      alt: e.target.value,
                    },
                  })
                }
                placeholder="Mô tả chi tiết về hình ảnh"
                rows={3}
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "video":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="video-src">URL video</Label>
              <Input
                id="video-src"
                value={(node.properties?.src as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      src: e.target.value,
                    },
                  })
                }
                placeholder="https://example.com/video.mp4"
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "imageWithCaption":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="caption-image-src">URL hình ảnh</Label>
              <Input
                id="caption-image-src"
                value={(node.properties?.src as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      src: e.target.value,
                    },
                  })
                }
                placeholder="https://example.com/image.jpg"
                disabled={disabled}
              />
            </div>
            <div>
              <Label htmlFor="caption-image-alt">Mô tả ảnh (Alt text)</Label>
              <Input
                id="caption-image-alt"
                value={(node.properties?.alt as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      alt: e.target.value,
                    },
                  })
                }
                placeholder="Mô tả ngắn gọn về hình ảnh"
                disabled={disabled}
              />
            </div>
            <div>
              <Label htmlFor="caption-text">Chú thích</Label>
              <Textarea
                id="caption-text"
                value={(node.properties?.caption as string) || ""}
                onChange={(e) =>
                  onChange({
                    properties: {
                      ...node.properties,
                      caption: e.target.value,
                    },
                  })
                }
                placeholder="Nhập chú thích cho hình ảnh..."
                rows={3}
                disabled={disabled}
              />
            </div>
          </div>
        );

      case "doublePanelsCard":
        return <DoublePanelsCardContentForm node={node} onChange={onChange} disabled={disabled} />;

      case "libraryDisplay":
        return <LibraryDisplayContentForm node={node} onChange={onChange} disabled={disabled} />;

      case "newsCustomGridDisplay":
        return <NewsCustomGridDisplayContentForm node={node} onChange={onChange} disabled={disabled} />;

      case "newsGridDisplay":
        return <NewsGridDisplayContentForm node={node} onChange={onChange} disabled={disabled} />;

      default:
        return (
          <div className="text-sm text-gray-500">
            Nội dung cho node type "{node.type}" đang được phát triển...
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {disabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
          <p className="text-sm text-yellow-800">
            Đang ở chế độ xem. Chuyển sang chế độ Sửa để thay đổi.
          </p>
        </div>
      )}
      {renderContentByType()}
    </div>
  );
};

/**
 * DoublePanelsCardContentForm - Content form for double panels card
 */
const DoublePanelsCardContentForm: React.FC<ContentFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const [selectedPanel, setSelectedPanel] = useState<'left' | 'right'>('left');

  // Get current panel data
  const leftPanelType = (node.properties?.leftPanelType as string) || "text";
  const leftPanelTitle = (node.properties?.leftPanelTitle as string) || "";
  const leftPanelContent = (node.properties?.leftPanelContent as string) || "";

  const rightPanelType = (node.properties?.rightPanelType as string) || "text";
  const rightPanelTitle = (node.properties?.rightPanelTitle as string) || "";
  const rightPanelContent = (node.properties?.rightPanelContent as string) || "";

  const leftWidth = (node.properties?.leftWidth as number) || 50;

  const updatePanelProperty = (panel: 'left' | 'right', property: string, value: string | number) => {
    const propertyKey = `${panel}Panel${property.charAt(0).toUpperCase() + property.slice(1)}`;
    onChange({
      properties: {
        ...node.properties,
        [propertyKey]: value,
      },
    });
  };

  const updateLeftWidth = (value: number) => {
    onChange({
      properties: {
        ...node.properties,
        leftWidth: value,
      },
    });
  };

  const currentPanelType = selectedPanel === 'left' ? leftPanelType : rightPanelType;
  const currentPanelTitle = selectedPanel === 'left' ? leftPanelTitle : rightPanelTitle;
  const currentPanelContent = selectedPanel === 'left' ? leftPanelContent : rightPanelContent;

  return (
    <div className="space-y-6">
      {/* Panel Selection */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Chọn panel để chỉnh sửa</Label>
        <div className="flex gap-2">
          <Button
            type="button"
            variant={selectedPanel === 'left' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedPanel('left')}
            disabled={disabled}
            className="flex-1"
          >
            Panel Trái ({leftWidth}%)
          </Button>
          <Button
            type="button"
            variant={selectedPanel === 'right' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedPanel('right')}
            disabled={disabled}
            className="flex-1"
          >
            Panel Phải ({100 - leftWidth}%)
          </Button>
        </div>
      </div>

      {/* Width Slider */}
      <div>
        <Label className="text-sm font-medium mb-3 block">
          Tỷ lệ chiều rộng: {leftWidth}% - {100 - leftWidth}%
        </Label>
        <input
          title="Kéo thanh trượt để thay đổi tỷ lệ chiều rộng của hai panel"
          type="range"
          value={leftWidth}
          onChange={(e) => updateLeftWidth(Number(e.target.value))}
          min={10}
          max={90}
          step={5}
          disabled={disabled}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>10%</span>
          <span>50%</span>
          <span>90%</span>
        </div>
      </div>

      {/* Panel Content Configuration */}
      <div className="border-t pt-4">
        <Label className="text-sm font-medium mb-3 block">
          Cấu hình {selectedPanel === 'left' ? 'Panel Trái' : 'Panel Phải'}
        </Label>

        {/* Content Type */}
        <div className="space-y-4">
          <div>
            <Label htmlFor="panel-type">Loại nội dung</Label>
            <Select
              value={currentPanelType}
              onValueChange={(value) => updatePanelProperty(selectedPanel, 'type', value)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn loại nội dung" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Văn bản</SelectItem>
                <SelectItem value="image">Hình ảnh</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Title */}
          <div>
            <Label htmlFor="panel-title">Tiêu đề (tùy chọn)</Label>
            <Input
              id="panel-title"
              value={currentPanelTitle}
              onChange={(e) => updatePanelProperty(selectedPanel, 'title', e.target.value)}
              placeholder="Nhập tiêu đề..."
              disabled={disabled}
            />
          </div>

          {/* Content */}
          <div>
            <Label htmlFor="panel-content">
              {currentPanelType === 'image' ? 'URL hình ảnh' : 'Nội dung văn bản'}
            </Label>
            {currentPanelType === 'image' ? (
              <Input
                id="panel-content"
                value={currentPanelContent}
                onChange={(e) => updatePanelProperty(selectedPanel, 'content', e.target.value)}
                placeholder="https://example.com/image.jpg"
                disabled={disabled}
              />
            ) : (
              <Textarea
                id="panel-content"
                value={currentPanelContent}
                onChange={(e) => updatePanelProperty(selectedPanel, 'content', e.target.value)}
                placeholder="Nhập nội dung văn bản..."
                rows={4}
                disabled={disabled}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * LibraryDisplayContentForm - Content form for library display
 */
const LibraryDisplayContentForm: React.FC<ContentFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  // Get current properties
  const title = (node.properties?.title as string) || "";
  const description = (node.properties?.description as string) || "";
  const buttonUrl = (node.properties?.buttonUrl as string) || "";
  const imagePosition = (node.properties?.imagePosition as string) || "right";
  const image1Url = (node.properties?.image1Url as string) || "";
  const image2Url = (node.properties?.image2Url as string) || "";
  const image3Url = (node.properties?.image3Url as string) || "";

  const updateProperty = (property: string, value: string) => {
    onChange({
      properties: {
        ...node.properties,
        [property]: value,
      },
    });
  };

  // URL validation helper
  const isValidUrl = (url: string): boolean => {
    if (!url.trim()) return true; // Empty is valid
    try {
      new URL(url);
      return true;
    } catch {
      return url.startsWith('/') || url.startsWith('#'); // Allow relative URLs
    }
  };

  const getUrlValidationMessage = (url: string): string | null => {
    if (!url.trim()) return null;
    if (!isValidUrl(url)) {
      return "Please enter a valid URL (e.g., https://example.com or /path)";
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Title */}
      <div>
        <Label htmlFor="library-title">Tiêu đề</Label>
        <Input
          id="library-title"
          value={title}
          onChange={(e) => updateProperty('title', e.target.value)}
          placeholder="Nhập tiêu đề..."
          disabled={disabled}
        />
      </div>

      {/* Description */}
      <div>
        <Label htmlFor="library-description">Mô tả</Label>
        <Textarea
          id="library-description"
          value={description}
          onChange={(e) => updateProperty('description', e.target.value)}
          placeholder="Nhập mô tả..."
          rows={4}
          disabled={disabled}
        />
      </div>

      {/* Image Position Toggle */}
      <div>
        <Label className="text-sm font-medium mb-3 block">Vị trí hình ảnh</Label>
        <div className="flex gap-2" role="radiogroup" aria-label="Image position">
          <Button
            type="button"
            variant={imagePosition === 'left' ? 'default' : 'outline'}
            size="sm"
            onClick={() => updateProperty('imagePosition', 'left')}
            disabled={disabled}
            className="flex-1"
            role="radio"
            aria-checked={imagePosition === 'left'}
            onKeyDown={(e) => {
              if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                e.preventDefault();
                updateProperty('imagePosition', 'right');
              }
            }}
          >
            Bên trái
          </Button>
          <Button
            type="button"
            variant={imagePosition === 'right' ? 'default' : 'outline'}
            size="sm"
            onClick={() => updateProperty('imagePosition', 'right')}
            disabled={disabled}
            className="flex-1"
            role="radio"
            aria-checked={imagePosition === 'right'}
            onKeyDown={(e) => {
              if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                e.preventDefault();
                updateProperty('imagePosition', 'left');
              }
            }}
          >
            Bên phải
          </Button>
        </div>
      </div>

      {/* Image URLs */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">URL hình ảnh</Label>

        <div>
          <Label htmlFor="image1-url" className="text-xs text-gray-600">Hình ảnh 1</Label>
          <Input
            id="image1-url"
            value={image1Url}
            onChange={(e) => updateProperty('image1Url', e.target.value)}
            placeholder="https://example.com/image1.jpg"
            disabled={disabled}
          />
        </div>

        <div>
          <Label htmlFor="image2-url" className="text-xs text-gray-600">Hình ảnh 2</Label>
          <Input
            id="image2-url"
            value={image2Url}
            onChange={(e) => updateProperty('image2Url', e.target.value)}
            placeholder="https://example.com/image2.jpg"
            disabled={disabled}
          />
        </div>

        <div>
          <Label htmlFor="image3-url" className="text-xs text-gray-600">Hình ảnh 3</Label>
          <Input
            id="image3-url"
            value={image3Url}
            onChange={(e) => updateProperty('image3Url', e.target.value)}
            placeholder="https://example.com/image3.jpg"
            disabled={disabled}
          />
        </div>
      </div>

      {/* Button URL */}
      <div>
        <Label htmlFor="button-url">URL nút "Xem thêm"</Label>
        <Input
          id="button-url"
          value={buttonUrl}
          onChange={(e) => updateProperty('buttonUrl', e.target.value)}
          placeholder="https://example.com"
          disabled={disabled}
          className={cn(
            !isValidUrl(buttonUrl) && buttonUrl.trim() && "border-red-500 focus:border-red-500"
          )}
        />
        {getUrlValidationMessage(buttonUrl) && (
          <p className="text-sm text-red-600 mt-1">
            {getUrlValidationMessage(buttonUrl)}
          </p>
        )}
      </div>
    </div>
  );
};

/**
 * NewsCustomGridDisplayContentForm - Content form for news custom grid display
 */
const NewsCustomGridDisplayContentForm: React.FC<ContentFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const header = (node.properties?.header as string) || "TIN HOẠT ĐỘNG";

  const updateProperty = (property: string, value: string) => {
    onChange({
      properties: {
        ...node.properties,
        [property]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="news-custom-header">Tiêu đề hiển thị</Label>
        <Input
          id="news-custom-header"
          value={header}
          onChange={(e) => updateProperty('header', e.target.value)}
          placeholder="Nhập tiêu đề hiển thị..."
          disabled={disabled}
        />
      </div>
    </div>
  );
};

/**
 * NewsGridDisplayContentForm - Content form for news grid display
 */
const NewsGridDisplayContentForm: React.FC<ContentFormProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const header = (node.properties?.header as string) || "TIN TRONG NGÀNH";
  const numberOfPosts = (node.properties?.numberOfPosts as number) || 3;

  const updateProperty = (property: string, value: string | number) => {
    onChange({
      properties: {
        ...node.properties,
        [property]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="news-grid-header">Tiêu đề hiển thị</Label>
        <Input
          id="news-grid-header"
          value={header}
          onChange={(e) => updateProperty('header', e.target.value)}
          placeholder="Nhập tiêu đề hiển thị..."
          disabled={disabled}
        />
      </div>
      <div>
        <Label htmlFor="news-grid-posts">Số bài viết hiển thị</Label>
        <Input
          id="news-grid-posts"
          type="number"
          value={numberOfPosts}
          onChange={(e) => updateProperty('numberOfPosts', parseInt(e.target.value) || 3)}
          placeholder="3"
          min="1"
          max="12"
          disabled={disabled}
        />
      </div>
    </div>
  );
};
