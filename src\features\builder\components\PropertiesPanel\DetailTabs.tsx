import React from "react";
import { TreeNode, TreeNodeType } from "../../context/types";
import { useUI } from "../../context/UIContext";
import { DisplayTabContent } from "./DisplayTabContent";
import { ContentForm } from "../forms/layouts/ContentForm";

interface DetailTabsProps {
  node: TreeNode;
  onChange: (updates: Partial<TreeNode>) => void;
  disabled: boolean;
}

/**
 * Helper function to determine node capabilities
 */
const getNodeConfiguration = (node: TreeNode) => {
  const nodeType = node.type;
  const nodeGroup = node.group;

  const hasContent = (type: TreeNodeType): boolean => {
    switch (type) {
      case "frame":
      case "dateTimeCard":
        return false;
      case "text":
      case "image":
      case "video":
      case "imageWithCaption":
      case "doublePanelsCard":
      case "libraryDisplay":
      case "newsCustomGridDisplay":
      case "newsGridDisplay":
        return true;
      default:
        return false;
    }
  };

  const getDefaultTab = (): "display" | "content" => {
    if (!hasContent(nodeType)) return "display";

    switch (nodeGroup) {
      case "basic":
        return "display";
      case "template":
        return "content";
      case "widget":
        return "display";
      default:
        return "display";
    }
  };

  return {
    hasContent: hasContent(nodeType),
    defaultTab: getDefaultTab(),
  };
};

/**
 * DetailTabs component manages the Display/Content sub-tabs
 * Used in the "Chi tiết" (Details) tab of the Properties Panel
 */
export const DetailTabs: React.FC<DetailTabsProps> = ({
  node,
  onChange,
  disabled,
}) => {
  const { uiState, setPropertiesPanelSubTab } = useUI();
  const config = getNodeConfiguration(node);
  const activeDetailTab =
    uiState?.temporary?.propertiesPanelSubTab ?? config.defaultTab;

  // Update active tab when node changes
  React.useEffect(() => {
    // Always prioritize "display" sub-tab when selecting nodes (like when creating new)
    if (activeDetailTab !== "display") {
      setPropertiesPanelSubTab("display");
    }
  }, [node.id]);

  const handleDetailTabChange = (tab: "display" | "content") => {
    // Prevent clicking disabled content tab
    if (tab === "content" && !config.hasContent) {
      return;
    }
    setPropertiesPanelSubTab(tab);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Sub Tabs - Clean underline style */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => handleDetailTabChange("display")}
          className={`flex-1 py-2 px-3 text-sm font-medium transition-all duration-200 border-b-2 ${activeDetailTab === "display"
              ? "text-blue-600 border-blue-600"
              : "text-gray-400 border-transparent hover:text-gray-600"
            }`}
        >
          Hiển thị
        </button>
        <button
          onClick={() => handleDetailTabChange("content")}
          disabled={!config.hasContent}
          className={`flex-1 py-2 px-3 text-sm font-medium transition-all duration-200 border-b-2 ${!config.hasContent
              ? "text-gray-300 border-transparent cursor-not-allowed opacity-50"
              : activeDetailTab === "content"
                ? "text-blue-600 border-blue-600"
                : "text-gray-400 border-transparent hover:text-gray-600"
            }`}
        >
          Nội dung
        </button>
      </div>

      {/* Tab Content */}
      <div className="flex-1">
        {activeDetailTab === "display" && (
          <DisplayTabContent
            node={node}
            onChange={onChange}
            disabled={disabled}
          />
        )}

        {activeDetailTab === "content" && config.hasContent && (
          <div className="p-3">
            <ContentForm node={node} onChange={onChange} disabled={disabled} />
          </div>
        )}
      </div>
    </div>
  );
};
