import { selectContactData, selectContactLoading } from "@/features/config/contact/states/selector";
import { fetchContactAsync } from "@/features/config/contact/states/slices";
import { selectHotlineData } from "@/features/config/hotline/states/selector";
import { fetchHotlineAsync } from "@/features/config/hotline/states/slices";
import { selectPortalLinkData } from "@/features/config/portal-link/states/selector";
import { fetchPortalLinkAsync } from "@/features/config/portal-link/states/slices";
import { ContactInfoDisplay, HotlineInfoDisplay, PortalLinkDisplay } from "@/features/config/shared/components";
import { Card01 } from "@/features/post/components/Card01";
import { Card02 } from "@/features/post/components/Card02";
import { mockPosts } from "@/features/post/states/mockData";
import { useAppDispatch, useAppSelector } from "@/store/rootReducer";
import { useEffect, useState } from "react";

// Import new demo components
import {
  DoublePanelsCard,
  LibraryDisplay,
  MostViewedDisplay,
  StatCard,
  ContactUsDisplay,
  ProcessingTicketDisplay,
  NewsCustomGridDisplay,
  NewsGridDisplay,
} from "../homepage/components";
import { mockLibraryItems, mockProcessingTickets, mockStats, ProcessingTicket, mockDoublePanelsData } from "../homepage/data/mockData";
import { toast } from "sonner";
import { Post } from "@/features/post/states/types";
import { fetchPublicPosts } from "@/features/post/states/api";
import { selectRefetch } from "@/features/post/states/selector";

const DemoPage = () => {
  const dispatch = useAppDispatch();
  const contactData = useAppSelector(selectContactData);
  const hotlineData = useAppSelector(selectHotlineData);
  const contactLoading = useAppSelector(selectContactLoading);
  const portalLinkData = useAppSelector(selectPortalLinkData);
  const refetch = useAppSelector(selectRefetch);

  const [postData, setPostData] = useState<Post[]>(mockPosts);

  useEffect(() => {
    if (!contactData && !contactLoading) {
      dispatch(fetchContactAsync());
    }
  }, [dispatch, contactData, contactLoading]);

  useEffect(() => {
    dispatch(fetchHotlineAsync());
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchPortalLinkAsync());
  }, [dispatch]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetchPublicPosts();
        setPostData(res.data || []);
      } catch (error) {
        console.error("Error in fetchPosts:", error);
        console.log('Using fallback mock data due to API failure');
        // Use mock data as fallback when API fails
        setPostData(mockPosts);
      }
    };
    fetchData();
  }, [refetch]);

  const handleContactSubmit = (data: { name: string; email: string; phone: string; subject: string; message: string }) => {
    console.log("Contact form submitted:", data);
    toast.success("Liên hệ đã được gửi thành công!");
  };

  const handleArticleClick = (post: Post) => {
    console.log("Article clicked:", post);
    toast.success(`Đã click vào bài viết: ${post.title}`);
  };

  const handleTicketClick = (ticket: ProcessingTicket) => {
    console.log("Ticket clicked:", ticket);
    toast.success(`Đã click vào phiếu: ${ticket.ticketName}`);
  };

  return (
    <div className="flex flex-col gap-12 p-6 bg-primary-foreground min-h-screen">
      <div className="max-w-7xl mx-auto w-full space-y-12">
        <h1 className="text-3xl font-bold text-center text-gray-900">Demo Components Showcase</h1>
        <Card01 posts={postData} />

        {/* Stats Cards Section */}
        <section className="flex flex-wrap gap-4 justify-between">
          {mockStats.map((stat, index) => (
            <StatCard
              key={index}
              number={stat.number}
              name={stat.name}
              description={stat.description}
              onClick={() => toast.success(`Clicked on ${stat.name}`)}
            />
          ))}
        </section>

        <PortalLinkDisplay
          title="LIÊN KẾT CÁC CỔNG"
          links={portalLinkData?.portalLinks || []}
        />

        {/* News Activity Display */}
        <NewsCustomGridDisplay
          title="TIN HOẠT ĐỘNG"
          posts={postData}
          onArticleClick={handleArticleClick}
        />

        {/* News Industry Display */}
        <NewsGridDisplay
          title="TIN TRONG NGÀNH"
          posts={postData}
          onArticleClick={handleArticleClick}
        />

        {/* Double Panels Card */}
        <section className="space-y-16">
          {mockDoublePanelsData.map((item, index) => (
            <DoublePanelsCard
              key={index}
              leftPanel={{
                type: item.leftPanel.type,
                content: item.leftPanel.content,
                title: item.leftPanel.title,
                size: item.leftPanel?.size,
              }}
              rightPanel={{
                type: item.rightPanel.type,
                content: item.rightPanel.content,
                title: item.rightPanel.title,
                size: item.rightPanel?.size,
              }}
            />
          ))}
        </section>

        {/* Library Display */}
        <LibraryDisplay
          items={mockLibraryItems}
        />

        <ContactUsDisplay onSubmit={handleContactSubmit} />

        {/* Most Viewed Display */}
        <section className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-800">Most Viewed Display</h2>
          <MostViewedDisplay
            posts={postData}
            maxImages={3}
            onClick={() => toast.success("Clicked most viewed display")}
          />
        </section>

        {/* Processing Tickets */}
        <section className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-800">Processing Tickets</h2>
          <ProcessingTicketDisplay
            tickets={mockProcessingTickets}
            onTicketClick={handleTicketClick}
          />
        </section>


        {/* Original Components */}
        <section className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-800">Original Components</h2>
          <div className="space-y-6">
            <HotlineInfoDisplay
              hotlines={hotlineData?.hotlines || []}
            />
            <ContactInfoDisplay
              contacts={contactData?.contacts || []}
            />
            <Card02 />
          </div>
        </section>
      </div>
    </div>
  );
};
export default DemoPage;
